import asyncio
from manager import Ed<PERSON>ireManager


async def main() -> None:
    print("=== EdSpire Job Search Assistant ===\n")
    
    # Get job type selection
    while True:
        job_type = input("Select job type (1 for Intern, 2 for New Grad): ").strip()
        if job_type == "1":
            job_type = "intern"
            break
        elif job_type == "2":
            job_type = "new_grad"
            break
        else:
            print("Please enter 1 or 2")
    
    # Get company preferences
    company_preferences = input("What types of companies are you looking for? ").strip()
    
    print(f"\nSearching for {job_type} positions matching: {company_preferences}")
    
    # Initialize and run EdSpireManager
    mgr = EdspireManager()
    await mgr.run_job_search(job_type, company_preferences)


if __name__ == "__main__":
    asyncio.run(main())
