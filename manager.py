from __future__ import annotations

import asyncio
import time
from collections.abc import Sequence

from rich.console import Console

from agents import Runner, custom_span, gen_trace_id, trace

from agents_custom.dataset_agent import dataset_agent, fetch_jobs
from agents_custom.web_agent import web_agent
from printer import Printer


class EdspireManager:
    """
    Orchestrates the full flow: planning, searching, sub‑analysis, writing, and verification.
    """

    def __init__(self) -> None:
        self.console = Console()
        self.printer = Printer(self.console)

    async def run_job_search(self, job_type: str, company_preferences: str) -> None:
        """Run the job search flow"""
        print(f"\n=== Fetching {job_type} job listings ===")
        
        # Step 1: Fetch job listings from dataset
        jobs = await fetch_jobs(job_type)
        if not jobs:
            print("No jobs found. Please try again later.")
            return
        
        print(f"Found {len(jobs)} recent job listings:")
        for i, job in enumerate(jobs, 1):
            print(f"{i}. {job.company} - {job.role} ({job.location})")
        
        # Step 2: Use web agent to analyze companies and match preferences
        print(f"\n=== Analyzing companies based on your preferences: {company_preferences} ===")
        
        # Create a simple analysis using the web agent
        analysis_prompt = f"""
        Analyze these companies based on the user's preferences: "{company_preferences}"
        
        Companies to analyze:
        {chr(10).join([f"- {job.company}: {job.role} in {job.location}" for job in jobs])}
        
        For each company, provide:
        1. Match score (1-10)
        2. Reasoning for the score
        """
        
        # For now, we'll do a simple analysis without calling external agents
        # In a full implementation, you would call the web_agent here
        
        print("\n=== Company Analysis Results ===")
        for job in jobs:
            # Simple scoring logic - in practice, this would use the web_agent
            score = 7  # Placeholder score
            reasoning = f"Company {job.company} offers {job.role} positions which may align with your interests in {company_preferences}"
            
            print(f"\n{job.company}")
            print(f"Match Score: {score}/10")
            print(f"Reasoning: {reasoning}")
            print(f"Position: {job.role}")
            print(f"Location: {job.location}")
            if job.application_url:
                print(f"Apply: {job.application_url}")

    async def run(self, query: str) -> None:
        trace_id = gen_trace_id()
        with trace("Financial research trace", trace_id=trace_id):
            self.printer.update_item(
                "trace_id",
                f"View trace: https://platform.openai.com/traces/trace?trace_id={trace_id}",
                is_done=True,
                hide_checkmark=True,
            )
            self.printer.update_item("start", "Starting financial research...", is_done=True)
            search_plan = await self._plan_searches(query)
            search_results = await self._perform_searches(search_plan)
            report = await self._write_report(query, search_results)
            verification = await self._verify_report(report)

            final_report = f"Report summary\n\n{report.short_summary}"
            self.printer.update_item("final_report", final_report, is_done=True)

            self.printer.end()

        # Print to stdout
        print("\n\n=====REPORT=====\n\n")
        print(f"Report:\n{report.markdown_report}")
        print("\n\n=====FOLLOW UP QUESTIONS=====\n\n")
        print("\n".join(report.follow_up_questions))
        print("\n\n=====VERIFICATION=====\n\n")
        print(verification)

    async def _plan_searches(self, query: str):
        self.printer.update_item("planning", "Planning searches...")
        # Placeholder implementation
        self.printer.update_item(
            "planning",
            "Planning completed",
            is_done=True,
        )
        return None

    async def _perform_searches(self, search_plan):
        self.printer.update_item("searching", "Performing searches...")
        # Placeholder implementation
        self.printer.update_item(
            "searching",
            "Searches completed",
            is_done=True,
        )
        return []

    async def _write_report(self, query: str, search_results):
        self.printer.update_item("writing", "Writing report...")
        # Placeholder implementation
        class Report:
            short_summary = "Placeholder report summary"
            markdown_report = "Placeholder markdown report"
            follow_up_questions = ["Placeholder question 1", "Placeholder question 2"]
        
        self.printer.update_item(
            "writing",
            "Report written",
            is_done=True,
        )
        return Report()

    async def _verify_report(self, report):
        self.printer.update_item("verifying", "Verifying report...")
        # Placeholder implementation
        self.printer.update_item(
            "verifying",
            "Verification completed",
            is_done=True,
        )
        return "Placeholder verification result"