import requests
from pydantic import BaseModel
from typing import List

from agents import Agent

# A sub‑agent focused on fetching job data from GitHub repositories
DATASET_PROMPT = (
    "You are a dataset agent. You fetch job listings from GitHub repositories and return "
    "the top 5 most recent job postings. You parse the markdown content and extract "
    "company names, roles, and locations."
)


class JobListing(BaseModel):
    company: str
    role: str
    location: str
    application_url: str


class DatasetSummary(BaseModel):
    jobs: List[JobListing]
    """List of top 5 most recent job listings."""


async def fetch_jobs(job_type: str) -> List[JobListing]:
    """Fetch job listings from the appropriate GitHub URL"""
    if job_type == "intern":
        url = "https://raw.githubusercontent.com/SimplifyJobs/Summer2026-Internships/refs/heads/dev/README.md"
    else:  # new_grad
        url = "https://raw.githubusercontent.com/SimplifyJobs/New-Grad-Positions/refs/heads/dev/README.md"
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        content = response.text
        
        # Parse the markdown table to extract job listings
        jobs = []
        lines = content.split('\n')
        in_table = False
        
        for line in lines:
            if '| Company' in line and '| Role' in line:
                in_table = True
                continue
            if in_table and line.strip() and '|' in line:
                parts = [part.strip() for part in line.split('|')]
                if len(parts) >= 4 and parts[1] and parts[2] and parts[3]:
                    company = parts[1].replace('**', '').replace('[', '').replace(']', '')
                    role = parts[2]
                    location = parts[3]
                    application_url = parts[4] if len(parts) > 4 else ""
                    
                    jobs.append(JobListing(
                        company=company,
                        role=role,
                        location=location,
                        application_url=application_url
                    ))
                    
                    if len(jobs) >= 5:  # Get top 5
                        break
        
        return jobs
    except Exception as e:
        print(f"Error fetching jobs: {e}")
        return []


dataset_agent = Agent(
    name="DatasetAgent",
    instructions=DATASET_PROMPT,
    output_type=DatasetSummary,
)